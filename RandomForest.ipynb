import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

df = pd.read_csv('fraudTrain.csv') 
test = pd.read_csv('fraudTest.csv')
print(df.head())
print (df.shape)
print(df.info())


########### for the test one #############
print(test.head())
print(test.shape)
print(test.info())

df.isnull().sum()  # this one is for the train

test.isnull().sum() # this one is for the test 

df.info()
test.info()

df['trans_date_trans_time'] = pd.to_datetime(df['trans_date_trans_time'])
df['trans_date'] = df['trans_date_trans_time'].dt.date
df['dob'] = pd.to_datetime(df['dob'])

# Apply the same preprocessing to test dataframe
test['trans_date_trans_time'] = pd.to_datetime(test['trans_date_trans_time'])
test['trans_date'] = test['trans_date_trans_time'].dt.date
test['dob'] = pd.to_datetime(test['dob'])

print(test['trans_date'].head(), test['dob'].head())

# removing the un named column  from the data set 

df = df.drop('Unnamed: 0', axis=1)

# Verify it’s gone
print(df.columns)


#  this code is for the finding the missing numeric columns 
df = df.drop_duplicates()

# Fill missing values for numeric columns only
numeric_cols = df.select_dtypes(include=['number']).columns
df[numeric_cols] = df[numeric_cols].fillna(df[numeric_cols].median())

print("missing values in the numeric columns are :" )
print(df[numeric_cols].isnull().sum())


# detecting the missing value from the non numeric data set 
categorical_columns = df.select_dtypes(exclude = ['number']).columns

for  col in categorical_columns: 
    df[col] = df[col].fillna(df[col].mode()[0])

print("missing value from non-numeric columns")
print(df[categorical_columns].isnull().sum())

# removing the credit card number column as well 
df = df.drop('cc_num' , axis = 1)
df.head()
# so tht it doesnt depend upon if the person was defrauded or not 



# FIRST: Convert to datetime (if not already done)
print("Converting datetime columns...")
df['trans_date_trans_time'] = pd.to_datetime(df['trans_date_trans_time'])
df['dob'] = pd.to_datetime(df['dob'])

# NOW: Time features from transaction timestamp
print("Creating time-based features...")
df['hour'] = df['trans_date_trans_time'].dt.hour
df['day_of_week'] = df['trans_date_trans_time'].dt.dayofweek
df['day_of_month'] = df['trans_date_trans_time'].dt.day
df['month'] = df['trans_date_trans_time'].dt.month

# Time features from date of birth
df['age'] = (df['trans_date_trans_time'] - df['dob']).dt.days // 365

print("\nNew time-based features added:")
print(f"  - hour (0-23)")
print(f"  - day_of_week (0=Monday, 6=Sunday)")
print(f"  - day_of_month (1-31)")
print(f"  - month (1-12)")
print(f"  - age (customer age in years)")

print("\nSample of new features:")
print(df[['trans_date_trans_time', 'hour', 'day_of_week', 'age']].head())

# this is for the train data
df["is_fraud_cat"] = df["is_fraud"].apply(lambda x : "T" if x ==  1 else "F")
df["is_fraud_cat"] = df["is_fraud_cat"].astype("object")
print(df[["is_fraud", "is_fraud_cat"]].head())


# this for the test data 
test["is_fraud_cat"] = test["is_fraud"].apply(lambda x : "T" if x ==  1 else "F")
test["is_fraud_cat"] = test["is_fraud_cat"].astype("object")
print(test[["is_fraud", "is_fraud_cat"]].head())


totalCat = df.select_dtypes(include=["object"])
df[totalCat.columns]


import seaborn as sns
import matplotlib.pyplot as plt

# Create count plot for fraud categories
sns.countplot(data=df[df['is_fraud_cat'] == 'T'], x='category')
plt.xticks(rotation=45)
plt.title('Distribution of Fraud Cases by Category')
plt.xlabel('Category')
plt.ylabel('Count')
plt.tight_layout()  # Prevents label cutoff
plt.show()

import seaborn as sns
import matplotlib.pyplot as plt

# Create a categorical version if needed
df['is_fraud_cat'] = df['is_fraud'].map({0: 'F', 1: 'T'})

# Custom color mapping
custom_palette = {"M": "blue", "F": "pink"}

# Countplot with custom colors
sns.countplot(
    x='gender',
    data=df[df['is_fraud_cat'] == 'T'],
    palette=custom_palette
)

plt.xticks(rotation=45)
plt.title("Fraudulent Transactions by Gender")
plt.show()


import seaborn as sns
import matplotlib.pyplot as plt

# Make sure to use the correct DataFrame
# Here I use 'total' as in your original snippet
fig, ax = plt.subplots(figsize=(20, 10))  # scaled down from 120,60 which is huge
plt.rcParams.update({'font.size': 14})    # font size appropriate

# Filter only fraud cases
fraud_data = df[df['is_fraud_cat'] == "T"]

# Create the countplot
sns.countplot(x='state', data=fraud_data, ax=ax)
plt.xticks(rotation=45)

# Add annotations on top of bars
state_counts = fraud_data['state'].value_counts()
for p in ax.patches:
    height = p.get_height()
    ax.annotate(f'{int(height)}', 
                (p.get_x() + p.get_width()/2, height + 0.5), 
                ha='center', va='bottom')

plt.title("Number of Credit Card Frauds by State")
plt.tight_layout()
plt.show()


import matplotlib.pyplot as plt
import random

# Function to generate a random RGB color
def randomcolor():
    return [random.random(), random.random(), random.random()]

# Get top 15  cities with fraud
top_cities = df[df['is_fraud_cat']=="T"]["city"].value_counts().head(15)

# Generate a random color for each bar
colors = [randomcolor() for _ in range(len(top_cities))]

# Plot
plt.rcParams.update({'font.size': 14})
top_cities.plot(kind="bar", color=colors)
plt.title("CC Fraud in City")  # your requested title
plt.xlabel("City")
plt.ylabel("Number of Frauds")
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()


df[df['is_fraud_cat']=="T"] ["job"].value_counts(sort = True , ascending = False).head(15).plot(kind= "bar" , color="orange")
plt.title("Top 15 Jobs with Fraudulent Transactions")
plt.show()

if 'is_fraud_cat' in df.columns:
	del df['is_fraud_cat']

from scipy.stats import norm, skew 

#finding the numerical columns 
testNumeric = test.select_dtypes(include=np.number)
print(testNumeric.columns)

df.isnull().sum() # this is for the train data set 


# Only select columns that exist in df
common_cols = testNumeric.columns.intersection(df.columns)
df[common_cols].info()

import pandas as pd
import numpy as np

print('Starting skew smoke test...')

try:
    df = pd.read_csv('fraudTrain.csv', nrows=20000)
    print('Loaded fraudTrain.csv with shape:', df.shape)
except FileNotFoundError:
    print('ERROR: fraudTrain.csv not found in working directory')
    raise

if 'Unnamed: 0' in df.columns:
    df = df.drop('Unnamed: 0', axis=1)

if 'is_fraud' not in df.columns:
    print('ERROR: is_fraud column missing')
    raise SystemExit(1)

y = df['is_fraud']
x = df.drop(columns=['is_fraud'])

if 'cc_num' in x.columns:
    x = x.drop(columns=['cc_num'])

numeric_features = x.select_dtypes(include=['number']).columns.tolist()
for col in ['zip','lat','long','unix_time','merch_lat','merch_long']:
    if col in numeric_features:
        numeric_features.remove(col)

print('amt in x columns:', 'amt' in x.columns)
print('amt in numeric_features before scaling:', 'amt' in numeric_features)

if numeric_features:
    means = x[numeric_features].mean()
    stds = x[numeric_features].std(ddof=0).replace(0, np.nan)
    x[numeric_features] = (x[numeric_features] - means) / stds

if 'amt' in df.columns and 'amt' in x.columns:
    before = df['amt'].skew()
    after = x['amt'].skew()
    print('Skewness amt before:', round(before, 4))
    print('Skewness amt after:', round(after, 4))
else:
    print('ERROR: amt not available for skewness test')



import seaborn as sns
import matplotlib.pyplot as plt
from scipy.stats import norm
from scipy.stats import skew
import numpy as np

plt.rcParams.update({'font.size': 15})

# Calculate skewness
skewness = str(round(skew(df['amt']), 2))

# Plot histogram
sns.histplot(df['amt'], kde=False, color='red', stat="density")

# Fit and plot a normal distribution curve
mu, sigma = norm.fit(df['amt'])
xmin, xmax = plt.xlim()
x = np.linspace(xmin, xmax, 100)
p = norm.pdf(x, mu, sigma)
plt.plot(x, p, 'k', linewidth=2)

plt.title('Amount Distribution  Skewness: ' + skewness)
plt.show()




# NOW ADD YOUR SKEWNESS ANALYSIS WITH CORRECTIONS:
import seaborn as sns
from scipy.stats import norm
from scipy.stats import skew

# BEFORE outlier removal - show original skewness
print(f"\nOriginal dataset shape after duplicates removed: {df.shape}")
original_skewness = skew(df['city_pop'])
print(f"Original city_pop skewness: {original_skewness:.2f}")

# Handle outliers to reduce skewness (this is what you're missing)
def handle_outliers_iqr(data, column):
    """Remove outliers using IQR method"""  # Interquartile Range
    Q1 = data[column].quantile(0.25)
    Q3 = data[column].quantile(0.75)
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR
    
    print(f"\nOutlier analysis for {column}:")
    print(f"Q1: {Q1:.2f}, Q3: {Q3:.2f}, IQR: {IQR:.2f}")
    print(f"Lower bound: {lower_bound:.2f}, Upper bound: {upper_bound:.2f}")
    
    # Count outliers
    outliers = data[(data[column] < lower_bound) | (data[column] > upper_bound)]
    print(f"Number of outliers: {len(outliers)} ({len(outliers)/len(data)*100:.2f}%)")
    
    # Remove outliers
    cleaned_data = data[(data[column] >= lower_bound) & (data[column] <= upper_bound)]
    return cleaned_data

# Apply outlier removal to city_pop
df_cleaned = handle_outliers_iqr(df, 'city_pop')
print(f"Dataset shape after outlier removal: {df_cleaned.shape}")

# Calculate skewness after outlier removal
skewness_after_cleaning = skew(df_cleaned['city_pop'])
skewness_str = str(round(skewness_after_cleaning, 2))

print(f"Skewness after outlier removal: {skewness_after_cleaning:.2f}")

# Create the plot
plt.figure(figsize=(10, 6))
sns.distplot(df_cleaned['city_pop'], fit=norm, color='green')
plt.title("City Population Skewness: " + skewness_str)
plt.xlabel('City Population')
plt.ylabel('Density')
plt.show()

# Optional: Show comparison with original data
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))

# Original data
sns.distplot(df['city_pop'], fit=norm, color='red', ax=ax1)
ax1.set_title(f"Original Data (Skewness: {original_skewness:.2f})")
ax1.set_xlabel('City Population')

# Cleaned data
sns.distplot(df_cleaned['city_pop'], fit=norm, color='green', ax=ax2)
ax2.set_title(f"After Outlier Removal (Skewness: {skewness_after_cleaning:.2f})")
ax2.set_xlabel('City Population')

plt.tight_layout()
plt.show()

# Update your dataframe to use the cleaned version
df = df_cleaned.copy()

print(f"\nFinal dataset shape: {df.shape}")
print(f"Final city_pop skewness: {skew(df['city_pop']):.2f}")

from sklearn.preprocessing import StandardScaler, MinMaxScaler

# Selecting the features and target values 
# Using is_fraud as target value while other all values are features

target = 'is_fraud'
y = df[target]

# Defining the feature and not including the target 
x = df.drop(columns=[target])

# ===== CHANGED PART: Scale ONLY specific numeric features =====
# Instead of auto-detecting all numeric columns, we explicitly choose which to scale
numeric_features_to_scale = ['amt', 'city_pop']

scaler = StandardScaler()

# Fit and transform ONLY these numeric features
x[numeric_features_to_scale] = scaler.fit_transform(x[numeric_features_to_scale])

# Now x contains scaled numeric features and untouched categorical/time features
print("Scaled numeric features:")
print(x[numeric_features_to_scale].head())



# one hot encoding 

import pandas as pd 

one_hot_column = ['gender','category','state']

# Check if x exists
print(f"x shape before encoding: {x.shape}")
print(f"x columns: {list(x.columns)[:10]}...")  # first 10 columns

#applying the one hot encoding 
x_encoded = pd.get_dummies(x , columns=one_hot_column , drop_first = True)

print("\n=== AFTER ONE HOT ENCODING ===")
print(f"Shape: {x_encoded.shape}")
print(f"\nColumns after encoding ({len(x_encoded.columns)} total):")
print(list(x_encoded.columns))
print("\nSample data:")
print(x_encoded.head())

#encoding for the high cardinality 

from sklearn.preprocessing import LabelEncoder

high_card_columns = x_encoded.select_dtypes(include=['object']).columns

for col in high_card_columns:
    le = LabelEncoder()
    x_encoded[col] = le.fit_transform(x_encoded[col].astype(str))


print("Columns after label encoding:")
print(x_encoded[high_card_columns].head())

from sklearn.model_selection import train_test_split
from imblearn.over_sampling import SMOTE

print("=== Step 1: Split data BEFORE applying SMOTE ===")
# Split the original data (NOT resampled)
X_train, X_test, y_train, y_test = train_test_split(
    x_encoded, 
    y, 
    test_size=0.2, 
    random_state=42, 
    stratify=y
)

print(f"Original training set: {X_train.shape}")
print(f"Original test set: {X_test.shape}")
print(f"\nOriginal training class distribution:")
print(y_train.value_counts())

print("\n=== Step 2: Apply SMOTE ONLY to training data ===")
# Apply SMOTE only to training data
smote = SMOTE(random_state=42)
X_train_resampled, y_train_resampled = smote.fit_resample(X_train, y_train)

print(f"\nResampled training set: {X_train_resampled.shape}")
print(f"Test set (unchanged): {X_test.shape}")
print(f"\nResampled training class distribution:")
print(pd.Series(y_train_resampled).value_counts())

print("\n✅ Data is now properly split - no data leakage!")


# train and splitting the data 

from sklearn.model_selection import train_test_split
# we will train 80 % of the data and test 20% 
X_train , X_test , y_train , y_test = train_test_split(X_resampled , y_resampled , test_size = 0.2  , random_state = 42 , stratify = y_resampled)
print("Training set:", X_train.shape, y_train.shape)
print("Testing set:", X_test.shape, y_test.shape)

from sklearn.ensemble import RandomForestClassifier

randomForest = RandomForestClassifier(
  n_estimators = 100 , # no of treees which are being used in  random forest
  max_depth = None , 
  class_weight = 'balanced', # it is used to handle the class imbalance
  random_state = 42,
  n_jobs =-1
)
randomForest.fit(X_train_resampled, y_train_resampled)

# Predictions
y_pred = randomForest.predict(X_test)
y_pred_proba = randomForest.predict_proba(X_test)[:, 1]  # probability of fraud


from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, confusion_matrix, classification_report

# Confusion Matrix
cm = confusion_matrix(y_test, y_pred)
print("Confusion Matrix:\n", cm)

# Accuracy
acc = accuracy_score(y_test, y_pred)
print("Accuracy:", acc)

# Precision
prec = precision_score(y_test, y_pred)
print("Precision:", prec)

# Recall
rec = recall_score(y_test, y_pred)
print("Recall:", rec)

# F1-Score
f1 = f1_score(y_test, y_pred)
print("F1-Score:", f1)

# ROC-AUC Score
roc_auc = roc_auc_score(y_test, y_pred_proba)
print("ROC-AUC Score:", roc_auc)

# Optional: full classification report
print("\nClassification Report:\n", classification_report(y_test, y_pred))


import matplotlib.pyplot as plt 

import numpy as np 

feature_importances = randomForest.feature_importances_
indices = np.argsort(feature_importances)[::-1]
featuredNames = X_resampled.columns

# adding the 15  imp plots

plt.figure(figsize=(12, 6))
plt.title("15 imp features")
plt.bar(range(15), 
		feature_importances[indices][:15], align="center")
plt.xticks(range(15), featuredNames[indices][:15], rotation=90)
plt.tight_layout()
plt.show()


print("\n" + "="*60)
print("EVALUATING ON REAL TEST SET (fraudTest.csv)")
print("="*60)

import pandas as pd
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, confusion_matrix, classification_report

# Load test set
test_df = pd.read_csv('fraudTest.csv')
print(f"\nTest set loaded: {test_df.shape}")

# Apply SAME preprocessing as training data
print("\n1. Dropping unnecessary columns...")
test_df = test_df.drop(['Unnamed: 0', 'cc_num'], axis=1, errors='ignore')

# Convert datetime
test_df['trans_date_trans_time'] = pd.to_datetime(test_df['trans_date_trans_time'])
test_df['dob'] = pd.to_datetime(test_df['dob'])

# Add same time features
print("2. Adding time features...")
test_df['hour'] = test_df['trans_date_trans_time'].dt.hour
test_df['day_of_week'] = test_df['trans_date_trans_time'].dt.dayofweek
test_df['day_of_month'] = test_df['trans_date_trans_time'].dt.day
test_df['month'] = test_df['trans_date_trans_time'].dt.month
test_df['age'] = (test_df['trans_date_trans_time'] - test_df['dob']).dt.days // 365

# Drop original datetime columns (cause RandomForest can't handle datetime)
test_df = test_df.drop(['trans_date_trans_time', 'dob'], axis=1)

# Separate features and target
y_test_real = test_df['is_fraud']
x_test_real = test_df.drop(columns=['is_fraud'])

# Scale numeric features (use SAME scaler from training)
print("3. Scaling numeric features...")
x_test_real[numeric_features_to_scale] = scaler.transform(x_test_real[numeric_features_to_scale])

# One-hot encode
print("4. One-hot encoding...")
x_test_encoded = pd.get_dummies(x_test_real, columns=['gender', 'category', 'state'], drop_first=True)

# Label encode high cardinality columns
print("5. Label encoding high cardinality columns...")
high_card_columns = x_test_encoded.select_dtypes(include=['object']).columns
for col in high_card_columns:
    le = LabelEncoder()
    # Fit on both train and test to handle unseen categories
    all_values = pd.concat([x_encoded[col].astype(str), x_test_encoded[col].astype(str)]).unique()
    le.fit(all_values)
    x_test_encoded[col] = le.transform(x_test_encoded[col].astype(str))

# Align columns with training data
print("6. Aligning columns with training data...")
x_test_encoded = x_test_encoded.reindex(columns=x_encoded.columns, fill_value=0)

# Make predictions
print("\n7. Making predictions on real test set...")
y_pred_real = randomForest.predict(x_test_encoded)

# Evaluate
print("\n" + "="*60)
print("REAL TEST SET PERFORMANCE")
print("="*60)

print(f"\nAccuracy:  {accuracy_score(y_test_real, y_pred_real):.4f}")
print(f"Precision: {precision_score(y_test_real, y_pred_real):.4f}")
print(f"Recall:    {recall_score(y_test_real, y_pred_real):.4f}")
print(f"F1-Score:  {f1_score(y_test_real, y_pred_real):.4f}")
print(f"ROC-AUC:   {roc_auc_score(y_test_real, y_pred_real):.4f}")

print("\nConfusion Matrix:")
print(confusion_matrix(y_test_real, y_pred_real))

print("\nClassification Report:")
print(classification_report(y_test_real, y_pred_real))

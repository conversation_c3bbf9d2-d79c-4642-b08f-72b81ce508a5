{"cells": [{"cell_type": "code", "execution_count": 21, "id": "733a5f98-43a7-40bc-8f96-d9e747af2fae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   Unnamed: 0 trans_date_trans_time            cc_num  \\\n", "0           0   2019-01-01 00:00:18  2703186189652095   \n", "1           1   2019-01-01 00:00:44      630423337322   \n", "2           2   2019-01-01 00:00:51    38859492057661   \n", "3           3   2019-01-01 00:01:16  3534093764340240   \n", "4           4   2019-01-01 00:03:06   375534208663984   \n", "\n", "                             merchant       category     amt      first  \\\n", "0          <PERSON>_<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>       misc_net    4.97   Jennifer   \n", "1     fraud_<PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>    grocery_pos  107.23  Stephanie   \n", "2                fraud_Lind-Buckridge  entertainment  220.11     Edward   \n", "3  fraud_<PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>  gas_transport   45.00     Jeremy   \n", "4                 fraud_Keeling-Crist       misc_pos   41.96      Tyler   \n", "\n", "      last gender                        street  ...      lat      long  \\\n", "0    Banks      F                561 Perry Cove  ...  36.0788  -81.1781   \n", "1     Gill      F  43039 Riley Greens Suite 393  ...  48.8878 -118.2105   \n", "2  <PERSON>      594 <PERSON> Dale Suite 530  ...  42.1808 -112.2620   \n", "3    <PERSON>      M   9443 Cynthia Court Apt. 038  ...  46.2306 -112.1138   \n", "4   <PERSON>      M              408 Bradley Rest  ...  38.4207  -79.4629   \n", "\n", "   city_pop                                job         dob  \\\n", "0      3495          <PERSON>sycho<PERSON>, counselling  1988-03-09   \n", "1       149  Special educational needs teacher  1978-06-21   \n", "2      4154        Nature conservation officer  1962-01-19   \n", "3      1939                    Patent attorney  1967-01-12   \n", "4        99     Dance movement psychotherapist  1986-03-28   \n", "\n", "                          trans_num   unix_time  merch_lat  merch_long  \\\n", "0  0b242abb623afc578575680df30655b9  **********  36.011293  -82.048315   \n", "1  1f76529f8574734946361c461b024d99  1325376044  49.159047 -118.186462   \n", "2  a1a22d70485983eac12b5b88dad1cf95  1325376051  43.150704 -112.154481   \n", "3  6b849c168bdad6f867558c3793159a81  1325376076  47.034331 -112.561071   \n", "4  a41d7549acf90789359a9aa5346dcb46  1325376186  38.674999  -78.632459   \n", "\n", "   is_fraud  \n", "0         0  \n", "1         0  \n", "2         0  \n", "3         0  \n", "4         0  \n", "\n", "[5 rows x 23 columns]\n", "(1296675, 23)\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 1296675 entries, 0 to 1296674\n", "Data columns (total 23 columns):\n", " #   Column                 Non-Null Count    Dtype  \n", "---  ------                 --------------    -----  \n", " 0   Unnamed: 0             1296675 non-null  int64  \n", " 1   trans_date_trans_time  1296675 non-null  object \n", " 2   cc_num                 1296675 non-null  int64  \n", " 3   merchant               1296675 non-null  object \n", " 4   category               1296675 non-null  object \n", " 5   amt                    1296675 non-null  float64\n", " 6   first                  1296675 non-null  object \n", " 7   last                   1296675 non-null  object \n", " 8   gender                 1296675 non-null  object \n", " 9   street                 1296675 non-null  object \n", " 10  city                   1296675 non-null  object \n", " 11  state                  1296675 non-null  object \n", " 12  zip                    1296675 non-null  int64  \n", " 13  lat                    1296675 non-null  float64\n", " 14  long                   1296675 non-null  float64\n", " 15  city_pop               1296675 non-null  int64  \n", " 16  job                    1296675 non-null  object \n", " 17  dob                    1296675 non-null  object \n", " 18  trans_num              1296675 non-null  object \n", " 19  unix_time              1296675 non-null  int64  \n", " 20  merch_lat              1296675 non-null  float64\n", " 21  merch_long             1296675 non-null  float64\n", " 22  is_fraud               1296675 non-null  int64  \n", "dtypes: float64(5), int64(6), object(12)\n", "memory usage: 227.5+ MB\n", "None\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "df = pd.read_csv('fraudTrain.csv') \n", "\n", "print(df.head())\n", "print (df.shape)\n", "print(df.info())\n", "\n"]}, {"cell_type": "code", "execution_count": 23, "id": "0bce83fe-580b-48a4-8946-68e59a24a93a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Index(['trans_date_trans_time', 'cc_num', 'merchant', 'category', 'amt',\n", "       'first', 'last', 'gender', 'street', 'city', 'state', 'zip', 'lat',\n", "       'long', 'city_pop', 'job', 'dob', 'trans_num', 'unix_time', 'merch_lat',\n", "       'merch_long', 'is_fraud'],\n", "      dtype='object')\n"]}], "source": ["# removing the un named column  from the data set \n", "\n", "df = df.drop('Unnamed: 0', axis=1)\n", "\n", "# Verify it’s gone\n", "print(df.columns)\n"]}, {"cell_type": "code", "execution_count": 19, "id": "a263f2b0-ab1f-4289-8f2f-38c16e493f1f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["missing values in the numeric columns are :\n", "Unnamed: 0    0\n", "cc_num        0\n", "amt           0\n", "zip           0\n", "lat           0\n", "long          0\n", "city_pop      0\n", "unix_time     0\n", "merch_lat     0\n", "merch_long    0\n", "is_fraud      0\n", "dtype: int64\n"]}], "source": ["#  this code is for the finding the missing numeric columns \n", "df = df.drop_duplicates()\n", "\n", "# Fill missing values for numeric columns only\n", "numeric_cols = df.select_dtypes(include=['number']).columns\n", "df[numeric_cols] = df[numeric_cols].fillna(df[numeric_cols].median())\n", "\n", "print(\"missing values in the numeric columns are :\" )\n", "print(df[numeric_cols].isnull().sum())\n"]}, {"cell_type": "code", "execution_count": 24, "id": "e1511964-3f2f-4a25-97ec-3b7c64a3b626", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["missing value from non-numeric columns\n", "trans_date_trans_time    0\n", "merchant                 0\n", "category                 0\n", "first                    0\n", "last                     0\n", "gender                   0\n", "street                   0\n", "city                     0\n", "state                    0\n", "job                      0\n", "dob                      0\n", "trans_num                0\n", "dtype: int64\n"]}], "source": ["# detecting the missing value from the non numeric data set \n", "categorical_columns = df.select_dtypes(exclude = ['number']).columns\n", "\n", "for  col in categorical_columns: \n", "    df[col] = df[col].fillna(df[col].mode()[0])\n", "\n", "print(\"missing value from non-numeric columns\")\n", "print(df[categorical_columns].isnull().sum())"]}, {"cell_type": "code", "execution_count": 29, "id": "cf4ac1ca-d695-45a6-b65e-7c737f68efad", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "\"['cc_num'] not found in axis\"", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                  <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[29]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# removing the credit card number column as well \u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m df = \u001b[43mdf\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdrop\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mcc_num\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m \u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[32;43m1\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m      3\u001b[39m df.head()\n\u001b[32m      4\u001b[39m \u001b[38;5;66;03m# so tht it doesnt depend upon if the person was defrauded or not \u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pandas\\core\\frame.py:5588\u001b[39m, in \u001b[36mDataFrame.drop\u001b[39m\u001b[34m(self, labels, axis, index, columns, level, inplace, errors)\u001b[39m\n\u001b[32m   5440\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mdrop\u001b[39m(\n\u001b[32m   5441\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   5442\u001b[39m     labels: IndexLabel | \u001b[38;5;28;01mNone\u001b[39;00m = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m   (...)\u001b[39m\u001b[32m   5449\u001b[39m     errors: IgnoreRaise = \u001b[33m\"\u001b[39m\u001b[33mraise\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m   5450\u001b[39m ) -> DataFrame | \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m   5451\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m   5452\u001b[39m \u001b[33;03m    Drop specified labels from rows or columns.\u001b[39;00m\n\u001b[32m   5453\u001b[39m \n\u001b[32m   (...)\u001b[39m\u001b[32m   5586\u001b[39m \u001b[33;03m            weight  1.0     0.8\u001b[39;00m\n\u001b[32m   5587\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m5588\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdrop\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   5589\u001b[39m \u001b[43m        \u001b[49m\u001b[43mlabels\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlabels\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   5590\u001b[39m \u001b[43m        \u001b[49m\u001b[43maxis\u001b[49m\u001b[43m=\u001b[49m\u001b[43maxis\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   5591\u001b[39m \u001b[43m        \u001b[49m\u001b[43mindex\u001b[49m\u001b[43m=\u001b[49m\u001b[43mindex\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   5592\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcolumns\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcolumns\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   5593\u001b[39m \u001b[43m        \u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   5594\u001b[39m \u001b[43m        \u001b[49m\u001b[43minplace\u001b[49m\u001b[43m=\u001b[49m\u001b[43minplace\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   5595\u001b[39m \u001b[43m        \u001b[49m\u001b[43merrors\u001b[49m\u001b[43m=\u001b[49m\u001b[43merrors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   5596\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pandas\\core\\generic.py:4807\u001b[39m, in \u001b[36mNDFrame.drop\u001b[39m\u001b[34m(self, labels, axis, index, columns, level, inplace, errors)\u001b[39m\n\u001b[32m   4805\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m axis, labels \u001b[38;5;129;01min\u001b[39;00m axes.items():\n\u001b[32m   4806\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m labels \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m4807\u001b[39m         obj = \u001b[43mobj\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_drop_axis\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlabels\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merrors\u001b[49m\u001b[43m=\u001b[49m\u001b[43merrors\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   4809\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m inplace:\n\u001b[32m   4810\u001b[39m     \u001b[38;5;28mself\u001b[39m._update_inplace(obj)\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pandas\\core\\generic.py:4849\u001b[39m, in \u001b[36mNDFrame._drop_axis\u001b[39m\u001b[34m(self, labels, axis, level, errors, only_slice)\u001b[39m\n\u001b[32m   4847\u001b[39m         new_axis = axis.drop(labels, level=level, errors=errors)\n\u001b[32m   4848\u001b[39m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m4849\u001b[39m         new_axis = \u001b[43maxis\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdrop\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlabels\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merrors\u001b[49m\u001b[43m=\u001b[49m\u001b[43merrors\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   4850\u001b[39m     indexer = axis.get_indexer(new_axis)\n\u001b[32m   4852\u001b[39m \u001b[38;5;66;03m# Case for non-unique axis\u001b[39;00m\n\u001b[32m   4853\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:7136\u001b[39m, in \u001b[36mIndex.drop\u001b[39m\u001b[34m(self, labels, errors)\u001b[39m\n\u001b[32m   7134\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m mask.any():\n\u001b[32m   7135\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m errors != \u001b[33m\"\u001b[39m\u001b[33mignore\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m-> \u001b[39m\u001b[32m7136\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01m<PERSON>eyError\u001b[39;00m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mlabels[mask].tolist()\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m not found in axis\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m   7137\u001b[39m     indexer = indexer[~mask]\n\u001b[32m   7138\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m.delete(indexer)\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m: \"['cc_num'] not found in axis\""]}], "source": ["# removing the credit card number column as well \n", "df = df.drop('cc_num' , axis = 1)\n", "df.head()\n", "# so tht it doesnt depend upon if the person was defrauded or not "]}, {"cell_type": "code", "execution_count": null, "id": "c274c24c-6c16-4c5c-ad57-258ab222ccff", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "29520154-b5d5-462d-9b53-bf01e6a50a6b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}
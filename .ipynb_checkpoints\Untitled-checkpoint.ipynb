{"cells": [{"cell_type": "code", "execution_count": 2, "id": "733a5f98-43a7-40bc-8f96-d9e747af2fae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   Unnamed: 0 trans_date_trans_time            cc_num  \\\n", "0           0   2019-01-01 00:00:18  2703186189652095   \n", "1           1   2019-01-01 00:00:44      630423337322   \n", "2           2   2019-01-01 00:00:51    38859492057661   \n", "3           3   2019-01-01 00:01:16  3534093764340240   \n", "4           4   2019-01-01 00:03:06   375534208663984   \n", "\n", "                             merchant       category     amt      first  \\\n", "0          <PERSON>_<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>       misc_net    4.97   Jennifer   \n", "1     fraud_<PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>    grocery_pos  107.23  Stephanie   \n", "2                fraud_Lind-Buckridge  entertainment  220.11     Edward   \n", "3  fraud_<PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>  gas_transport   45.00     Jeremy   \n", "4                 fraud_Keeling-Crist       misc_pos   41.96      Tyler   \n", "\n", "      last gender                        street  ...      lat      long  \\\n", "0    Banks      F                561 Perry Cove  ...  36.0788  -81.1781   \n", "1     Gill      F  43039 Riley Greens Suite 393  ...  48.8878 -118.2105   \n", "2  <PERSON>      594 <PERSON> Dale Suite 530  ...  42.1808 -112.2620   \n", "3    <PERSON>      M   9443 Cynthia Court Apt. 038  ...  46.2306 -112.1138   \n", "4   <PERSON>      M              408 Bradley Rest  ...  38.4207  -79.4629   \n", "\n", "   city_pop                                job         dob  \\\n", "0      3495          <PERSON>sycho<PERSON>, counselling  1988-03-09   \n", "1       149  Special educational needs teacher  1978-06-21   \n", "2      4154        Nature conservation officer  1962-01-19   \n", "3      1939                    Patent attorney  1967-01-12   \n", "4        99     Dance movement psychotherapist  1986-03-28   \n", "\n", "                          trans_num   unix_time  merch_lat  merch_long  \\\n", "0  0b242abb623afc578575680df30655b9  **********  36.011293  -82.048315   \n", "1  1f76529f8574734946361c461b024d99  1325376044  49.159047 -118.186462   \n", "2  a1a22d70485983eac12b5b88dad1cf95  1325376051  43.150704 -112.154481   \n", "3  6b849c168bdad6f867558c3793159a81  1325376076  47.034331 -112.561071   \n", "4  a41d7549acf90789359a9aa5346dcb46  1325376186  38.674999  -78.632459   \n", "\n", "   is_fraud  \n", "0         0  \n", "1         0  \n", "2         0  \n", "3         0  \n", "4         0  \n", "\n", "[5 rows x 23 columns]\n", "(1296675, 23)\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 1296675 entries, 0 to 1296674\n", "Data columns (total 23 columns):\n", " #   Column                 Non-Null Count    Dtype  \n", "---  ------                 --------------    -----  \n", " 0   Unnamed: 0             1296675 non-null  int64  \n", " 1   trans_date_trans_time  1296675 non-null  object \n", " 2   cc_num                 1296675 non-null  int64  \n", " 3   merchant               1296675 non-null  object \n", " 4   category               1296675 non-null  object \n", " 5   amt                    1296675 non-null  float64\n", " 6   first                  1296675 non-null  object \n", " 7   last                   1296675 non-null  object \n", " 8   gender                 1296675 non-null  object \n", " 9   street                 1296675 non-null  object \n", " 10  city                   1296675 non-null  object \n", " 11  state                  1296675 non-null  object \n", " 12  zip                    1296675 non-null  int64  \n", " 13  lat                    1296675 non-null  float64\n", " 14  long                   1296675 non-null  float64\n", " 15  city_pop               1296675 non-null  int64  \n", " 16  job                    1296675 non-null  object \n", " 17  dob                    1296675 non-null  object \n", " 18  trans_num              1296675 non-null  object \n", " 19  unix_time              1296675 non-null  int64  \n", " 20  merch_lat              1296675 non-null  float64\n", " 21  merch_long             1296675 non-null  float64\n", " 22  is_fraud               1296675 non-null  int64  \n", "dtypes: float64(5), int64(6), object(12)\n", "memory usage: 227.5+ MB\n", "None\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "df = pd.read_csv('fraudTrain.csv') \n", "\n", "print(df.head())\n", "print (df.shape)\n", "print(df.info())\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "id": "0bce83fe-580b-48a4-8946-68e59a24a93a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Index(['trans_date_trans_time', 'cc_num', 'merchant', 'category', 'amt',\n", "       'first', 'last', 'gender', 'street', 'city', 'state', 'zip', 'lat',\n", "       'long', 'city_pop', 'job', 'dob', 'trans_num', 'unix_time', 'merch_lat',\n", "       'merch_long', 'is_fraud'],\n", "      dtype='object')\n"]}], "source": ["# removing the un named column  from the data set \n", "\n", "df = df.drop('Unnamed: 0', axis=1)\n", "\n", "# Verify it’s gone\n", "print(df.columns)\n"]}, {"cell_type": "code", "execution_count": 4, "id": "a263f2b0-ab1f-4289-8f2f-38c16e493f1f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["missing values in the numeric columns are :\n", "cc_num        0\n", "amt           0\n", "zip           0\n", "lat           0\n", "long          0\n", "city_pop      0\n", "unix_time     0\n", "merch_lat     0\n", "merch_long    0\n", "is_fraud      0\n", "dtype: int64\n"]}], "source": ["#  this code is for the finding the missing numeric columns \n", "df = df.drop_duplicates()\n", "\n", "# Fill missing values for numeric columns only\n", "numeric_cols = df.select_dtypes(include=['number']).columns\n", "df[numeric_cols] = df[numeric_cols].fillna(df[numeric_cols].median())\n", "\n", "print(\"missing values in the numeric columns are :\" )\n", "print(df[numeric_cols].isnull().sum())\n"]}, {"cell_type": "code", "execution_count": 5, "id": "e1511964-3f2f-4a25-97ec-3b7c64a3b626", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["missing value from non-numeric columns\n", "trans_date_trans_time    0\n", "merchant                 0\n", "category                 0\n", "first                    0\n", "last                     0\n", "gender                   0\n", "street                   0\n", "city                     0\n", "state                    0\n", "job                      0\n", "dob                      0\n", "trans_num                0\n", "dtype: int64\n"]}], "source": ["# detecting the missing value from the non numeric data set \n", "categorical_columns = df.select_dtypes(exclude = ['number']).columns\n", "\n", "for  col in categorical_columns: \n", "    df[col] = df[col].fillna(df[col].mode()[0])\n", "\n", "print(\"missing value from non-numeric columns\")\n", "print(df[categorical_columns].isnull().sum())"]}, {"cell_type": "code", "execution_count": 6, "id": "cf4ac1ca-d695-45a6-b65e-7c737f68efad", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>trans_date_trans_time</th>\n", "      <th>merchant</th>\n", "      <th>category</th>\n", "      <th>amt</th>\n", "      <th>first</th>\n", "      <th>last</th>\n", "      <th>gender</th>\n", "      <th>street</th>\n", "      <th>city</th>\n", "      <th>state</th>\n", "      <th>...</th>\n", "      <th>lat</th>\n", "      <th>long</th>\n", "      <th>city_pop</th>\n", "      <th>job</th>\n", "      <th>dob</th>\n", "      <th>trans_num</th>\n", "      <th>unix_time</th>\n", "      <th>merch_lat</th>\n", "      <th>merch_long</th>\n", "      <th>is_fraud</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2019-01-01 00:00:18</td>\n", "      <td><PERSON>_<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON></td>\n", "      <td>misc_net</td>\n", "      <td>4.97</td>\n", "      <td><PERSON></td>\n", "      <td>Banks</td>\n", "      <td>F</td>\n", "      <td>561 Perry Cove</td>\n", "      <td>Moravian Falls</td>\n", "      <td>NC</td>\n", "      <td>...</td>\n", "      <td>36.0788</td>\n", "      <td>-81.1781</td>\n", "      <td>3495</td>\n", "      <td>Psychologist, counselling</td>\n", "      <td>1988-03-09</td>\n", "      <td>0b242abb623afc578575680df30655b9</td>\n", "      <td>**********</td>\n", "      <td>36.011293</td>\n", "      <td>-82.048315</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2019-01-01 00:00:44</td>\n", "      <td><PERSON>_<PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON></td>\n", "      <td>grocery_pos</td>\n", "      <td>107.23</td>\n", "      <td><PERSON></td>\n", "      <td>Gill</td>\n", "      <td>F</td>\n", "      <td>43039 Riley Greens Suite 393</td>\n", "      <td>Orient</td>\n", "      <td>WA</td>\n", "      <td>...</td>\n", "      <td>48.8878</td>\n", "      <td>-118.2105</td>\n", "      <td>149</td>\n", "      <td>Special educational needs teacher</td>\n", "      <td>1978-06-21</td>\n", "      <td>1f76529f8574734946361c461b024d99</td>\n", "      <td>1325376044</td>\n", "      <td>49.159047</td>\n", "      <td>-118.186462</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2019-01-01 00:00:51</td>\n", "      <td>fraud_<PERSON><PERSON>-<PERSON><PERSON></td>\n", "      <td>entertainment</td>\n", "      <td>220.11</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>M</td>\n", "      <td>594 White Dale Suite 530</td>\n", "      <td>Malad City</td>\n", "      <td>ID</td>\n", "      <td>...</td>\n", "      <td>42.1808</td>\n", "      <td>-112.2620</td>\n", "      <td>4154</td>\n", "      <td>Nature conservation officer</td>\n", "      <td>1962-01-19</td>\n", "      <td>a1a22d70485983eac12b5b88dad1cf95</td>\n", "      <td>1325376051</td>\n", "      <td>43.150704</td>\n", "      <td>-112.154481</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2019-01-01 00:01:16</td>\n", "      <td><PERSON>_<PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON></td>\n", "      <td>gas_transport</td>\n", "      <td>45.00</td>\n", "      <td><PERSON></td>\n", "      <td>White</td>\n", "      <td>M</td>\n", "      <td>9443 Cynthia Court Apt. 038</td>\n", "      <td>Boulder</td>\n", "      <td>MT</td>\n", "      <td>...</td>\n", "      <td>46.2306</td>\n", "      <td>-112.1138</td>\n", "      <td>1939</td>\n", "      <td>Patent attorney</td>\n", "      <td>1967-01-12</td>\n", "      <td>6b849c168bdad6f867558c3793159a81</td>\n", "      <td>1325376076</td>\n", "      <td>47.034331</td>\n", "      <td>-112.561071</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2019-01-01 00:03:06</td>\n", "      <td>fraud_Keeling-Crist</td>\n", "      <td>misc_pos</td>\n", "      <td>41.96</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>M</td>\n", "      <td>408 Bradley Rest</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>VA</td>\n", "      <td>...</td>\n", "      <td>38.4207</td>\n", "      <td>-79.4629</td>\n", "      <td>99</td>\n", "      <td>Dance movement psychotherapist</td>\n", "      <td>1986-03-28</td>\n", "      <td>a41d7549acf90789359a9aa5346dcb46</td>\n", "      <td>1325376186</td>\n", "      <td>38.674999</td>\n", "      <td>-78.632459</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 21 columns</p>\n", "</div>"], "text/plain": ["  trans_date_trans_time                            merchant       category  \\\n", "0   2019-01-01 00:00:18          <PERSON>_<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>       misc_net   \n", "1   2019-01-01 00:00:44     <PERSON>_<PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>me    grocery_pos   \n", "2   2019-01-01 00:00:51                fraud_Lind-Buckridge  entertainment   \n", "3   2019-01-01 00:01:16  <PERSON>_<PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>  gas_transport   \n", "4   2019-01-01 00:03:06                 fraud_Keeling-Crist       misc_pos   \n", "\n", "      amt      first     last gender                        street  \\\n", "0    4.97   <PERSON>      F                561 <PERSON>   \n", "1  107.23  <PERSON>      F  43039 Riley Greens Suite 393   \n", "2  220.11     <PERSON>      M      594 <PERSON> Dale Suite 530   \n", "3   45.00     <PERSON>      M   9443 Cynthia <PERSON> Apt. 038   \n", "4   41.96      <PERSON>      M              408 <PERSON>   \n", "\n", "             city state  ...      lat      long  city_pop  \\\n", "0  Moravian Falls    NC  ...  36.0788  -81.1781      3495   \n", "1          Orient    WA  ...  48.8878 -118.2105       149   \n", "2      Malad City    ID  ...  42.1808 -112.2620      4154   \n", "3         Boulder    MT  ...  46.2306 -112.1138      1939   \n", "4        Doe Hill    VA  ...  38.4207  -79.4629        99   \n", "\n", "                                 job         dob  \\\n", "0          Psychologist, counselling  1988-03-09   \n", "1  Special educational needs teacher  1978-06-21   \n", "2        Nature conservation officer  1962-01-19   \n", "3                    Patent attorney  1967-01-12   \n", "4     Dance movement psychotherapist  1986-03-28   \n", "\n", "                          trans_num   unix_time  merch_lat  merch_long  \\\n", "0  0b242abb623afc578575680df30655b9  **********  36.011293  -82.048315   \n", "1  1f76529f8574734946361c461b024d99  1325376044  49.159047 -118.186462   \n", "2  a1a22d70485983eac12b5b88dad1cf95  1325376051  43.150704 -112.154481   \n", "3  6b849c168bdad6f867558c3793159a81  1325376076  47.034331 -112.561071   \n", "4  a41d7549acf90789359a9aa5346dcb46  1325376186  38.674999  -78.632459   \n", "\n", "   is_fraud  \n", "0         0  \n", "1         0  \n", "2         0  \n", "3         0  \n", "4         0  \n", "\n", "[5 rows x 21 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# removing the credit card number column as well \n", "df = df.drop('cc_num' , axis = 1)\n", "df.head()\n", "# so tht it doesnt depend upon if the person was defrauded or not "]}, {"cell_type": "code", "execution_count": 7, "id": "c274c24c-6c16-4c5c-ad57-258ab222ccff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Scaled numeric features:\n", "        amt  city_pop\n", "0 -0.407826 -0.282589\n", "1  0.230039 -0.293670\n", "2  0.934149 -0.280406\n", "3 -0.158132 -0.287742\n", "4 -0.177094 -0.293835\n", "Target values:\n", "0    0\n", "1    0\n", "2    0\n", "3    0\n", "4    0\n", "Name: is_fraud, dtype: int64\n"]}], "source": ["from sklearn.preprocessing import StandardScaler, MinMaxScaler\n", "\n", "#selecting the features and target values \n", "# using is_fraud as target value  while other all values are features\n", "\n", "target = 'is_fraud'\n", "y = df[target]\n", "\n", "# defining the feature and not including the target \n", "x = df.drop(columns=[target])\n", "\n", "#selecting the numeric columns to perform scaling \n", "numeric_features = x.select_dtypes(include= ['int64' , 'float64']).columns\n", "\n", "#dropping the columns such as trans_num, zip\n", "numeric_features = numeric_features.drop(['zip' , 'lat' , 'long' , 'unix_time' , 'merch_lat' , 'merch_long'])\n", "\n", "\n", "scaler = StandardScaler()\n", "\n", "# Fit and transform numeric features\n", "x[numeric_features] = scaler.fit_transform(x[numeric_features])\n", "\n", "# Now X contains scaled numeric features and untouched categorical features\n", "print(\"Scaled numeric features:\")\n", "print(x[numeric_features].head())\n", "\n", "print(\"Target values:\")\n", "print(y.head())\n"]}, {"cell_type": "code", "execution_count": 8, "id": "29520154-b5d5-462d-9b53-bf01e6a50a6b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["columns after the one hot encoding\n", "Index(['trans_date_trans_time', 'merchant', 'amt', 'first', 'last', 'street',\n", "       'city', 'zip', 'lat', 'long', 'city_pop', 'job', 'dob', 'trans_num',\n", "       'unix_time', 'merch_lat', 'merch_long', 'gender_M',\n", "       'category_food_dining', 'category_gas_transport',\n", "       'category_grocery_net', 'category_grocery_pos',\n", "       'category_health_fitness', 'category_home', 'category_kids_pets',\n", "       'category_misc_net', 'category_misc_pos', 'category_personal_care',\n", "       'category_shopping_net', 'category_shopping_pos', 'category_travel',\n", "       'state_AL', 'state_AR', 'state_AZ', 'state_CA', 'state_CO', 'state_CT',\n", "       'state_DC', 'state_DE', 'state_FL', 'state_GA', 'state_HI', 'state_IA',\n", "       'state_ID', 'state_IL', 'state_IN', 'state_KS', 'state_KY', 'state_LA',\n", "       'state_MA', 'state_<PERSON>', 'state_ME', 'state_MI', 'state_MN', 'state_MO',\n", "       'state_MS', 'state_MT', 'state_NC', 'state_ND', 'state_NE', 'state_NH',\n", "       'state_NJ', 'state_NM', 'state_NV', 'state_NY', 'state_OH', 'state_OK',\n", "       'state_OR', 'state_PA', 'state_RI', 'state_SC', 'state_SD', 'state_TN',\n", "       'state_TX', 'state_UT', 'state_VA', 'state_VT', 'state_WA', 'state_WI',\n", "       'state_WV', 'state_WY'],\n", "      dtype='object')\n", "sample data\n", "  trans_date_trans_time                            merchant       amt  \\\n", "0   2019-01-01 00:00:18          <PERSON>_<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON> -0.407826   \n", "1   2019-01-01 00:00:44     <PERSON>_<PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>  0.230039   \n", "2   2019-01-01 00:00:51                fraud_<PERSON>d-Buck<PERSON>  0.934149   \n", "3   2019-01-01 00:01:16  <PERSON>_<PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON> -0.158132   \n", "4   2019-01-01 00:03:06                 fraud_Keeling-Crist -0.177094   \n", "\n", "       first     last                        street            city    zip  \\\n", "0   <PERSON>                561 Perry Cove  Moravian Falls  28654   \n", "1  <PERSON>  43039 Riley Greens Suite 393          Orient  99160   \n", "2     <PERSON>      594 White Dale Suite 530      Malad City  83252   \n", "3     <PERSON>   9443 Cynthia <PERSON> Apt. 038         Boulder  59632   \n", "4      <PERSON>              408 Bradley Rest        Doe Hill  24433   \n", "\n", "       lat      long  ...  state_SD state_TN state_TX state_UT  state_VA  \\\n", "0  36.0788  -81.1781  ...     False    False    False    False     False   \n", "1  48.8878 -118.2105  ...     False    False    False    False     False   \n", "2  42.1808 -112.2620  ...     False    False    False    False     False   \n", "3  46.2306 -112.1138  ...     False    False    False    False     False   \n", "4  38.4207  -79.4629  ...     False    False    False    False      True   \n", "\n", "   state_VT  state_WA  state_WI  state_WV  state_WY  \n", "0     False     False     False     False     False  \n", "1     False      True     False     False     False  \n", "2     False     False     False     False     False  \n", "3     False     False     False     False     False  \n", "4     False     False     False     False     False  \n", "\n", "[5 rows x 81 columns]\n"]}], "source": ["# one hot encoding \n", "\n", "import pandas as pd \n", "\n", "one_hot_column = ['gender','category','state']\n", "\n", "#applying the one hot encoding \n", "x_encoded = pd.get_dummies(x , columns=one_hot_column , drop_first = True)\n", "\n", "print(\"columns after the one hot encoding\")\n", "print(x_encoded.columns)\n", "print(\"sample data\")\n", "print(x_encoded.head())"]}, {"cell_type": "code", "execution_count": 9, "id": "327c9f35-c53c-40e4-a3f1-00c5be4e3baa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Columns after label encoding:\n", "   trans_date_trans_time  merchant  first  last  street  city  job  dob  \\\n", "0                      0       514    162    18     568   526  370  779   \n", "1                      1       241    309   157     435   612  428  607   \n", "2                      2       390    115   381     602   468  307  302   \n", "3                      3       360    163   463     930    84  328  397   \n", "4                      4       297    336   149     418   216  116  734   \n", "\n", "   trans_num  \n", "0      56438  \n", "1     159395  \n", "2     818703  \n", "3     544575  \n", "4     831111  \n"]}], "source": ["#encoding for the high cardinality \n", "\n", "from sklearn.preprocessing import LabelEncoder\n", "\n", "high_card_columns = x_encoded.select_dtypes(include=['object']).columns\n", "\n", "for col in high_card_columns:\n", "    le = LabelEncoder()\n", "    x_encoded[col] = le.fit_transform(x_encoded[col].astype(str))\n", "\n", "\n", "print(\"Columns after label encoding:\")\n", "print(x_encoded[high_card_columns].head())"]}, {"cell_type": "markdown", "id": "66a0456a-c937-4dca-9717-622eef4f5996", "metadata": {}, "source": ["# Handling Imbalanced Data"]}, {"cell_type": "code", "execution_count": 10, "id": "e8a160f0-6841-40a6-b3e3-6b30b1a4f270", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original class distribution:\n", "is_fraud\n", "0    1289169\n", "1       7506\n", "Name: count, dtype: int64\n", "\n", "Resampled class distribution:\n", "is_fraud\n", "0    1289169\n", "1    1289169\n", "Name: count, dtype: int64\n"]}], "source": ["from imblearn.over_sampling import SMOTE\n", "import pandas as pd \n", "\n", "smote = SMOTE(random_state = 42)\n", "X_resampled, y_resampled = smote.fit_resample(x_encoded, y)\n", "print(\"Original class distribution:\")\n", "print(y.value_counts())\n", "\n", "print(\"\\nResampled class distribution:\")\n", "print(pd.Series(y_resampled).value_counts()) "]}, {"cell_type": "markdown", "id": "c496ce91-13f6-4fb8-a279-98a89ab97d95", "metadata": {}, "source": ["# random forest "]}, {"cell_type": "code", "execution_count": null, "id": "f8870d9f-a545-408d-851f-7b692a3402d4", "metadata": {}, "outputs": [], "source": ["# train and splitting the data \n", "\n", "from sklearn.model_selection import train_test_split\n", "# we will train 80 % of the data and test 20% \n", "X_train , X_test , y_train , y_test = train_test_split(X_resampled , y_resampled\n", "test_size = 0.2  , random_state = 42 , stratify = y_resampled)\n", "\n", "print(\"the training data set is :\" , X"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}